package com.yxt.order.assistant.server.knowledge.confluence;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import com.yxt.order.assistant.server.config.OrderAssistantConfig;
import com.yxt.order.assistant.server.repository.KnowledgeRepository;

import java.lang.reflect.Method;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
public class ConfluenceMarkdownExporterTest {

    @Mock
    private OrderAssistantConfig orderAssistantConfig;

    @Mock
    private KnowledgeRepository knowledgeRepository;

    @InjectMocks
    private ConfluenceMarkdownExporter exporter;

    private Method cleanupMarkdownMethod;
    private Method isHeadingMethod;

    @BeforeEach
    void setUp() throws Exception {
        // 使用反射获取私有方法
        cleanupMarkdownMethod = ConfluenceMarkdownExporter.class.getDeclaredMethod("cleanupMarkdown", String.class);
        cleanupMarkdownMethod.setAccessible(true);
        
        isHeadingMethod = ConfluenceMarkdownExporter.class.getDeclaredMethod("isHeading", String.class);
        isHeadingMethod.setAccessible(true);
    }

    @Test
    void testIsHeading() throws Exception {
        // 测试标题识别
        assertTrue((Boolean) isHeadingMethod.invoke(exporter, "# 一级标题"));
        assertTrue((Boolean) isHeadingMethod.invoke(exporter, "## 二级标题"));
        assertTrue((Boolean) isHeadingMethod.invoke(exporter, "### 三级标题"));
        assertTrue((Boolean) isHeadingMethod.invoke(exporter, "#### 四级标题"));
        assertTrue((Boolean) isHeadingMethod.invoke(exporter, "##### 五级标题"));
        assertTrue((Boolean) isHeadingMethod.invoke(exporter, "###### 六级标题"));
        
        // 测试非标题
        assertFalse((Boolean) isHeadingMethod.invoke(exporter, "普通文本"));
        assertFalse((Boolean) isHeadingMethod.invoke(exporter, "####### 七级标题")); // 超过6级
        assertFalse((Boolean) isHeadingMethod.invoke(exporter, "#没有空格的标题"));
        assertFalse((Boolean) isHeadingMethod.invoke(exporter, ""));
        assertFalse((Boolean) isHeadingMethod.invoke(exporter, null));
    }

    @Test
    void testCleanupMarkdown_RemoveEmptyLines() throws Exception {
        String input = "# 标题1\n\n\n内容1\n\n\n\n## 标题2\n\n内容2\n\n\n";
        String expected = "# 标题1\n内容1\n## 标题2\n内容2";
        
        String result = (String) cleanupMarkdownMethod.invoke(exporter, input);
        assertEquals(expected, result);
    }

    @Test
    void testCleanupMarkdown_RemoveEmptyHeadings() throws Exception {
        String input = "# 标题1\n## 空标题1\n### 空标题2\n## 有内容的标题\n这是内容\n### 另一个空标题\n## 最后的标题\n最后的内容";
        String expected = "# 标题1\n## 有内容的标题\n这是内容\n## 最后的标题\n最后的内容";
        
        String result = (String) cleanupMarkdownMethod.invoke(exporter, input);
        assertEquals(expected, result);
    }

    @Test
    void testCleanupMarkdown_ComplexCase() throws Exception {
        String input = "# 主标题\n\n\n## 空标题1\n\n\n### 空标题2\n\n\n## 有内容的标题\n\n\n这是一些内容\n\n\n还有更多内容\n\n\n### 子标题\n\n\n子标题内容\n\n\n## 另一个空标题\n\n\n# 最终标题\n\n\n最终内容\n\n\n";
        String expected = "# 主标题\n## 有内容的标题\n这是一些内容\n还有更多内容\n### 子标题\n子标题内容\n# 最终标题\n最终内容";
        
        String result = (String) cleanupMarkdownMethod.invoke(exporter, input);
        assertEquals(expected, result);
    }

    @Test
    void testCleanupMarkdown_EmptyInput() throws Exception {
        assertNull((String) cleanupMarkdownMethod.invoke(exporter, null));
        assertEquals("", (String) cleanupMarkdownMethod.invoke(exporter, ""));
        assertEquals("", (String) cleanupMarkdownMethod.invoke(exporter, "   "));
    }

    @Test
    void testCleanupMarkdown_OnlyHeadings() throws Exception {
        String input = "# 标题1\n## 标题2\n### 标题3";
        String expected = ""; // 所有标题都没有内容，应该全部被移除
        
        String result = (String) cleanupMarkdownMethod.invoke(exporter, input);
        assertEquals(expected, result);
    }

    @Test
    void testCleanupMarkdown_OnlyContent() throws Exception {
        String input = "这是内容1\n\n\n这是内容2\n\n\n这是内容3\n\n\n";
        String expected = "这是内容1\n这是内容2\n这是内容3";
        
        String result = (String) cleanupMarkdownMethod.invoke(exporter, input);
        assertEquals(expected, result);
    }
}

package com.yxt.order.assistant.server.knowledge.confluence;

import java.lang.reflect.Method;

/**
 * 测试Confluence宏处理功能
 */
public class ConfluenceMacroTest {

    public static void main(String[] args) {
        System.out.println("=== Confluence宏处理测试 ===\n");
        
        try {
            ConfluenceMarkdownExporter exporter = new ConfluenceMarkdownExporter();
            
            // 使用反射获取私有方法
            Method convertHtmlToMarkdownMethod = ConfluenceMarkdownExporter.class.getDeclaredMethod("convertHtmlToMarkdown", String.class, String.class);
            convertHtmlToMarkdownMethod.setAccessible(true);
            
            // 测试代码宏处理
            testCodeMacro(convertHtmlToMarkdownMethod, exporter);
            
            // 测试信息宏处理
            testInfoMacro(convertHtmlToMarkdownMethod, exporter);
            
            System.out.println("✅ 所有测试通过！");
            
        } catch (Exception e) {
            System.err.println("❌ 测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void testCodeMacro(Method method, ConfluenceMarkdownExporter exporter) throws Exception {
        System.out.println("📝 测试1: Confluence代码宏处理");
        
        String htmlWithCodeMacro = "<ac:structured-macro ac:name=\"code\" ac:schema-version=\"1\">" +
            "<ac:plain-text-body><![CDATA[@Data\n" +
            "public class BasePayEntity {\n" +
            "  private String orgCode;\n" +
            "  private String appId;\n" +
            "}]]></ac:plain-text-body>" +
            "</ac:structured-macro>";
        
        System.out.println("输入HTML:");
        System.out.println(htmlWithCodeMacro);
        
        String result = (String) method.invoke(exporter, htmlWithCodeMacro, "测试页面");
        System.out.println("\n输出Markdown:");
        System.out.println(result);
        System.out.println("\n" + "=".repeat(50) + "\n");
    }
    
    private static void testInfoMacro(Method method, ConfluenceMarkdownExporter exporter) throws Exception {
        System.out.println("📝 测试2: Confluence信息宏处理");
        
        String htmlWithInfoMacro = "<ac:structured-macro ac:name=\"info\">" +
            "<ac:rich-text-body>" +
            "<p>这是一个重要的信息提示</p>" +
            "</ac:rich-text-body>" +
            "</ac:structured-macro>";
        
        System.out.println("输入HTML:");
        System.out.println(htmlWithInfoMacro);
        
        String result = (String) method.invoke(exporter, htmlWithInfoMacro, "测试页面");
        System.out.println("\n输出Markdown:");
        System.out.println(result);
        System.out.println("\n" + "=".repeat(50) + "\n");
    }
    
    private static void testComplexExample(Method method, ConfluenceMarkdownExporter exporter) throws Exception {
        System.out.println("📝 测试3: 复杂示例（您提供的HTML内容）");
        
        String complexHtml = "<h1>背景</h1>" +
            "<p>产品经理小凯在某一天找到了你，说，小明啊，有个新的需求，很简单，给门店配置支付方式</p>" +
            "<ac:structured-macro ac:name=\"code\" ac:schema-version=\"1\">" +
            "<ac:plain-text-body><![CDATA[@Data\n" +
            "public class BasePayEntity {\n" +
            "  /**\n" +
            "   * 机构编码\n" +
            "   */\n" +
            "  private String orgCode;\n" +
            "  /**\n" +
            "   * appid\n" +
            "   */\n" +
            "  private String appId;\n" +
            "}]]></ac:plain-text-body>" +
            "</ac:structured-macro>" +
            "<h2>实体拆分</h2>" +
            "<p>拆分简单，对于接口通用字段抽象到父类，不同支付类型为子类即可</p>";
        
        System.out.println("输入HTML:");
        System.out.println(complexHtml);
        
        String result = (String) method.invoke(exporter, complexHtml, "测试页面");
        System.out.println("\n输出Markdown:");
        System.out.println(result);
        System.out.println("\n" + "=".repeat(50) + "\n");
    }
}

package com.yxt.order.assistant.server.knowledge.confluence;

/**
 * Test with the full HTML provided by user
 */
public class FullHtmlTest {

    public static void main(String[] args) {
        System.out.println("=== Full HTML Test ===\n");
        
        // The exact HTML you provided
        String fullHtml = "<h1>背景</h1><p>产品经理小凯在某一天找到了你，说，小明啊，有个新的需求，很简单，给门店配置支付方式，目前第一版本迭代，我们暂时只支持微信支付，我们需要保存的信息大概有以下几个：</p><p><ac:image ac:thumbnail=\"true\" ac:height=\"250\"><ri:attachment ri:filename=\"image-2024-3-27_15-27-39.png\" /></ac:image></p><p>小明在分析完需求之后，觉得这有何难，立马奋笔疾书，接口定义、入参实体定义、逻辑实现一气呵成。</p><p>这个时候的入参实体可能长这个样子：</p><ol><li><ac:image ac:height=\"400\"><ri:attachment ri:filename=\"image-2024-3-27_15-27-1.png\" /></ac:image></li></ol><p>然而没过两个月，小凯又来了，小明啊，新需求又来了，要新增一种医保支付方式，需要保存医保的信息如下:</p><p><ac:image ac:thumbnail=\"true\" ac:height=\"250\"><ri:attachment ri:filename=\"image-2024-3-27_15-38-10.png\" /></ac:image></p><p>小明接到需求之后，思索片刻，不就多了个类型，又有何难，缺啥字段补啥字段。</p><p>于是小明在第一版定义的接口入参中新增了医保相关的字段：</p><p><ac:image ac:height=\"400\"><ri:attachment ri:filename=\"image-2024-3-27_15-42-18.png\" /></ac:image></p><p>此时，入参实体已然有N多字段。</p><p>又过了不到两个月，小凯又过来了，小明啊，我们又又需要增加一个支付类型。。。。。。。。。。</p><h1>优化方案</h1><p>&nbsp; &nbsp; 问题其实很简单，随着业务的不断发展，接口的入参实体为了兼容多种支付类型，字段数量就会无限增长，但其实对于某一种支付类型来说，很多字段并没有用，而且对于后续维护都带来了困难，那我们就需要对入参实体按不同的支付类型进行拆分；</p><h2>实体拆分</h2><p>拆分简单，对于接口通用字段抽象到父类，不同支付类型为子类即可，简单示例如下：</p><ol><li>父类：<ol><li><p class=\"auto-cursor-target\"><br /></p><ac:structured-macro ac:name=\"code\" ac:schema-version=\"1\" ac:macro-id=\"15b7615e-a879-493a-bd8a-72aa1b48ba68\"><ac:plain-text-body><![CDATA[@Data\npublic class BasePayEntity {\n  /**\n   * 机构编码\n   */\n  private String orgCode;\n  /**\n   * appid\n   */\n  private String appId;\n  /**\n   * app密钥\n   */\n  private String appSecret;\n  /**\n   * 支付方式\n   */\n  private String payType;\n}]]></ac:plain-text-body></ac:structured-macro><p class=\"auto-cursor-target\"><br /></p></li></ol></li><li>微信子类：<ol><li><p class=\"auto-cursor-target\"><br /></p><ac:structured-macro ac:name=\"code\" ac:schema-version=\"1\" ac:macro-id=\"16983b3d-c03f-49d5-8a35-7ec71540048d\"><ac:plain-text-body><![CDATA[@EqualsAndHashCode(callSuper = true)\n@Data\npublic class WXPayEntity extends BasePayEntity {\n\n  /**\n   * 通道商户号\n   */\n  private String channelBusinessNo;\n  /**\n   * 公众号APPID\n   */\n  private String publicAppId;\n}]]></ac:plain-text-body></ac:structured-macro><p class=\"auto-cursor-target\"><br /></p></li></ol></li><li>支付宝子类：<ol><li><p class=\"auto-cursor-target\"><br /></p><ac:structured-macro ac:name=\"code\" ac:schema-version=\"1\" ac:macro-id=\"d7748e4a-6337-457d-a606-ce6df3ccb3dd\"><ac:plain-text-body><![CDATA[@EqualsAndHashCode(callSuper = true)\n@Data\npublic class ZFBPayEntity extends BasePayEntity {\n\n  /**\n   * 商户私钥\n   */\n  private String privateKey;\n  /**\n   * APIv3密钥\n   */\n  private String secretKey;\n}]]></ac:plain-text-body></ac:structured-macro><p class=\"auto-cursor-target\"><br /></p></li></ol></li></ol><p>子类拆分完毕，但是API如何自动映射到对应的请求体呢？</p><ol><li>使用Object/String接收，在代码中再根据类型自行反序列化，但我们作为程序员，主打一个优雅，这种方式属实不够优雅。</li><li>本文重点：多态反序列化</li></ol><h2>接口请求的多态反序列化</h2><ol><li>jackson的多态反序列化：主要使用两个注解实现：@JsonTypeInfo 和 @JsonSubTypes，对于这两个注解的详细介绍：<a href=\"https://www.cnblogs.com/lknny/p/5757784.html\">Jackson对多态和多子类序列化的处理配置 - toto怎么会喝醉 - 博客园 (cnblogs.com)</a>，<a href=\"https://blog.csdn.net/u010979642/article/details/110524277\">Jackson里使用@JsonTypeInfo注解处理多态类型的序列化和反序列化_jsontypeinfo注解用法-CSDN博客</a></li><li>简单示例：<ol><li>定义一个统一入参实体：<ol><li><p class=\"auto-cursor-target\"><br /></p><ac:structured-macro ac:name=\"code\" ac:schema-version=\"1\" ac:macro-id=\"ccf99caa-1b45-4343-bafe-2a02df3eb8d2\"><ac:plain-text-body><![CDATA[@Data\npublic class PayRequest<T extends BasePayEntity> {\n\n  private String payType;\n\n  @JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = As.EXTERNAL_PROPERTY, property = \"payType\")\n  private T payInfo;\n}]]></ac:plain-text-body></ac:structured-macro><p class=\"auto-cursor-target\"><br /></p></li></ol></li><li>父类BasePayEntity添加@JsonSubTypes注解，如下：<ol><li><p class=\"auto-cursor-target\"><br /></p><ac:structured-macro ac:name=\"code\" ac:schema-version=\"1\" ac:macro-id=\"a36d0524-3a73-4e34-a884-83b0ad10bf8d\"><ac:plain-text-body><![CDATA[@Data\n@JsonSubTypes(\n    {\n        @JsonSubTypes.Type(value = WXPayEntity.class, name = \"WX\"),\n        @JsonSubTypes.Type(value = ZFBPayEntity.class, name = \"ZFB\")\n    }\n)\npublic class BasePayEntity {\n  /**\n   * 机构id\n   */\n  private String orgCode;\n  /**\n   * 小程序appid\n   */\n  private String appId;\n  /**\n   * 公众号密钥\n   */\n  private String appSecret;\n}]]></ac:plain-text-body></ac:structured-macro><p class=\"auto-cursor-target\"><br /></p></li></ol></li><li>其余子类无变化</li><li>API示例：<ol><li><p class=\"auto-cursor-target\"><br /></p><ac:structured-macro ac:name=\"code\" ac:schema-version=\"1\" ac:macro-id=\"9a488803-bfe9-45c0-9c77-1fa146cf300b\"><ac:plain-text-body><![CDATA[  @PostMapping(\"/add\")\n  public <T extends BasePayEntity> void pay(@RequestBody PayRequest<T> request)\n      throws JsonProcessingException {\n    System.out.println(objectMapper.writeValueAsString(request));\n  }]]></ac:plain-text-body></ac:structured-macro><p class=\"auto-cursor-target\"><br /></p></li></ol></li><li>示例演示：<ol><li><ac:image ac:height=\"250\"><ri:attachment ri:filename=\"jackson多态反序列化.gif\" /></ac:image></li></ol></li></ol></li></ol><p><br /></p>";
        
        System.out.println("Processing full HTML...");
        
        ConfluenceMarkdownExporter exporter = new ConfluenceMarkdownExporter();
        String result = exporter.convertHtmlToMarkdown(fullHtml, "Test Page");
        
        System.out.println("\n=== RESULT ===");
        System.out.println(result);
        System.out.println("=== END RESULT ===\n");
        
        // Check for problematic patterns
        if (result.contains("```\n   \n   ```")) {
            System.out.println("❌ Found problematic empty code blocks!");
        } else {
            System.out.println("✅ No empty code blocks found");
        }
        
        // Count code blocks
        int codeBlockCount = countOccurrences(result, "```");
        System.out.println("Code block markers found: " + codeBlockCount);
        if (codeBlockCount % 2 != 0) {
            System.out.println("❌ Unmatched code block markers!");
        } else {
            System.out.println("✅ Code block markers are balanced");
        }
    }
    
    private static int countOccurrences(String text, String pattern) {
        int count = 0;
        int index = 0;
        while ((index = text.indexOf(pattern, index)) != -1) {
            count++;
            index += pattern.length();
        }
        return count;
    }
}

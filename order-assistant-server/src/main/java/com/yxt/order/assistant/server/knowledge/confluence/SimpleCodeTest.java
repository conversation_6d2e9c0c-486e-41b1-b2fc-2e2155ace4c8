package com.yxt.order.assistant.server.knowledge.confluence;

import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

/**
 * Simple test for code macro processing
 */
public class SimpleCodeTest {

    public static void main(String[] args) {
        System.out.println("=== Code Macro Test ===\n");
        
        // Test HTML with code macro
        String htmlWithCode = "<h1>Background</h1>" +
            "<p>Product manager came to you with a new requirement</p>" +
            "<ac:structured-macro ac:name=\"code\">" +
            "<ac:plain-text-body><![CDATA[@Data\n" +
            "public class BasePayEntity {\n" +
            "  private String orgCode;\n" +
            "  private String appId;\n" +
            "}]]></ac:plain-text-body>" +
            "</ac:structured-macro>" +
            "<h2>Entity Split</h2>" +
            "<p>Split is simple, abstract common fields to parent class</p>";
        
        System.out.println("Input HTML:");
        System.out.println(htmlWithCode);
        System.out.println("\n" + "=".repeat(60) + "\n");
        
        // Parse HTML
        Document doc = Jsoup.parse(htmlWithCode);
        
        // Find code macros
        Elements codeMacros = doc.select("ac\\:structured-macro[ac\\:name=code]");
        System.out.println("Found " + codeMacros.size() + " code macro(s)");
        
        for (Element macro : codeMacros) {
            Elements plainTextBodies = macro.select("ac\\:plain-text-body");
            if (!plainTextBodies.isEmpty()) {
                String codeContent = extractCDataContent(plainTextBodies.first());
                System.out.println("Extracted code:");
                System.out.println("```");
                System.out.println(codeContent);
                System.out.println("```");
            }
        }
        
        System.out.println("\n=== Test Complete ===");
    }
    
    private static String extractCDataContent(Element element) {
        String html = element.html();
        
        int cdataStart = html.indexOf("<![CDATA[");
        if (cdataStart != -1) {
            int contentStart = cdataStart + 9; // "<![CDATA[".length()
            int cdataEnd = html.indexOf("]]>", contentStart);
            if (cdataEnd != -1) {
                return html.substring(contentStart, cdataEnd);
            }
        }
        
        return element.text();
    }
}

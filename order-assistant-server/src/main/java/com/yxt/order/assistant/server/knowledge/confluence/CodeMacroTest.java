package com.yxt.order.assistant.server.knowledge.confluence;

/**
 * 简单测试代码宏处理
 */
public class CodeMacroTest {

    public static void main(String[] args) {
        System.out.println("=== 代码宏处理测试 ===\n");
        
        ConfluenceMarkdownExporter exporter = new ConfluenceMarkdownExporter();
        
        // 测试简单的代码宏
        System.out.println("📝 测试1: 简单代码宏");
        String simpleCodeHtml = "<ac:structured-macro ac:name=\"code\">" +
            "<ac:plain-text-body><![CDATA[@Data\n" +
            "public class BasePayEntity {\n" +
            "  private String orgCode;\n" +
            "}]]></ac:plain-text-body>" +
            "</ac:structured-macro>";
        
        String result1 = exporter.convertHtmlToMarkdown(simpleCodeHtml, "测试");
        System.out.println("结果:");
        System.out.println("'" + result1 + "'");
        System.out.println();
        
        // 测试带标题和代码的混合内容
        System.out.println("📝 测试2: 标题+代码混合");
        String mixedHtml = "<h2>父类定义</h2>" +
            "<p>以下是父类的代码：</p>" +
            "<ac:structured-macro ac:name=\"code\">" +
            "<ac:plain-text-body><![CDATA[@Data\n" +
            "public class BasePayEntity {\n" +
            "  private String orgCode;\n" +
            "  private String appId;\n" +
            "}]]></ac:plain-text-body>" +
            "</ac:structured-macro>" +
            "<p>这是父类的说明。</p>";
        
        String result2 = exporter.convertHtmlToMarkdown(mixedHtml, "测试");
        System.out.println("结果:");
        System.out.println("'" + result2 + "'");
        System.out.println();
        
        // 测试多个代码块
        System.out.println("📝 测试3: 多个代码块");
        String multiCodeHtml = "<h1>实体拆分</h1>" +
            "<h2>父类</h2>" +
            "<ac:structured-macro ac:name=\"code\">" +
            "<ac:plain-text-body><![CDATA[@Data\n" +
            "public class BasePayEntity {\n" +
            "  private String orgCode;\n" +
            "}]]></ac:plain-text-body>" +
            "</ac:structured-macro>" +
            "<h2>子类</h2>" +
            "<ac:structured-macro ac:name=\"code\">" +
            "<ac:plain-text-body><![CDATA[@Data\n" +
            "public class WXPayEntity extends BasePayEntity {\n" +
            "  private String channelBusinessNo;\n" +
            "}]]></ac:plain-text-body>" +
            "</ac:structured-macro>";
        
        String result3 = exporter.convertHtmlToMarkdown(multiCodeHtml, "测试");
        System.out.println("结果:");
        System.out.println("'" + result3 + "'");
        
        System.out.println("\n✅ 测试完成！");
    }
}
